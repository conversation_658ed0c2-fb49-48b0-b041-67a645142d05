// src/hooks/useAuth.ts
import { useEffect, useState } from 'react';
import { User, onAuthStateChanged } from 'firebase/auth';
import { doc, setDoc, getDoc, updateDoc, collection, query, where, getDocs, Firestore } from 'firebase/firestore';
import { auth, db } from '../firebase';
import { useTranslation } from 'react-i18next';
import { TFunction } from 'i18next';

// Assurez-vous que cette fonction est exportée si vous l'utilisez ailleurs
export async function generateAnonymousPseudo(uid: string, t: TFunction, db: Firestore): Promise<string> {
  const adjectivesObj = t('pseudoGenerator.adjectives', { returnObjects: true }) as Record<string, string>;
  const nounsObj = t('pseudoGenerator.nouns', { returnObjects: true }) as Record<string, string>;

  const adjectives = Object.values(adjectivesObj);
  const nouns = Object.values(nounsObj);

  if (adjectives.length === 0 || nouns.length === 0) {
    console.warn("Translation keys for pseudo generator (adjectives/nouns) are empty or incorrect. Using fallback.");
    return `User_${uid.substring(0, 4)}_${Math.random().toString(16).substring(2, 6).toUpperCase()}`;
  }

  const MAX_RETRIES = 10;
  for (let i = 0; i < MAX_RETRIES; i++) {
    const randomAdjective = adjectives[Math.floor(Math.random() * adjectives.length)];
    const randomNoun = nouns[Math.floor(Math.random() * nouns.length)];
    const randomHexSuffix = Math.random().toString(16).substring(2, 6).toUpperCase();

    const potentialPseudo = `${randomAdjective} ${randomNoun} ${randomHexSuffix}`;

    const usersRef = collection(db, 'users');
    const q = query(usersRef, where('publicName', '==', potentialPseudo));
    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      return potentialPseudo;
    }
  }

  console.warn("Could not generate a unique pseudo after multiple retries. Using ultimate fallback.");
  return `FallbackUser_${uid.substring(0, 4)}_${Date.now().toString(16).substring(0, 4).toUpperCase()}`;
}


export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const { t } = useTranslation();

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      setUser(firebaseUser);
      setLoading(false);

      if (firebaseUser) {
        const userDocRef = doc(db, 'users', firebaseUser.uid);
        const userDocSnap = await getDoc(userDocRef);

        if (!userDocSnap.exists()) {
          console.log("Creating new user document in Firestore for:", firebaseUser.uid);
          const initialPublicName = await generateAnonymousPseudo(firebaseUser.uid, t, db);

          await setDoc(userDocRef, {
            email: firebaseUser.email,
            createdAt: new Date().toISOString(),
            lastLoginAt: new Date().toISOString(),
            publicName: initialPublicName,
            preferences: {
              theme: 'light',
              language: 'fr',
              notificationsEnabled: true,
              preferedGender: 'masculine',
              audioConfig: { // <-- NOUVELLE STRUCTURE AUDIO CONFIG
                enableMusic: false,
                music: { volume: 50, url: '' }, // Initialisez avec une URL vide si nécessaire
                enableAmbient: false,
                ambient: { volume: 50, url: '' },
                enableBinaural: false,
                binaural: { volume: 50, baseFreq: 100, beatFreq: 10 },
                voice: { volume: 100, language: 'fr', provider: 'browser', voice: 'auto', gender: 'auto' },
              },
              unlockedFeatures: [],
              subscriptions: {
                active: false,
                startDate: null,
                endsAt: null,
                cancelAtPeriodEnd: false,
                tier: 'free',
              },
              adWatchData: {
                lastAdWatchedAt: null,
                adFreeUntil: null,
                adsWatchedToday: 0,
                lastAdWatchDay: null,
              },
              stats: {
                totalSessionsPlayed: 0,
                totalMinutesPlayed: 0,
                lastSessionPlayedAt: null,
                currentStreak: 0,
                longestStreak: 0,
                totalNotesWritten: 0,
                totalSessionsWithNotes: 0,
                favoriteSessionId: [],
              },
            },
          });
        } else {
            await updateDoc(userDocRef, {
                lastLoginAt: new Date().toISOString()
            });

            const userData = userDocSnap.data();
            const updates: any = {};
            if (!userData.publicName) {
                const newPublicName = await generateAnonymousPseudo(firebaseUser.uid, t, db);
                updates.publicName = newPublicName;
                console.log("Generated and updated publicName for existing user:", firebaseUser.uid, newPublicName);
            }
            // MISE À JOUR : Initialisation des nouvelles propriétés pour les utilisateurs existants qui n'ont pas encore ces champs
            if (!userData.preferences || !userData.preferences.subscriptions) {
                updates['preferences.subscriptions'] = {
                  active: false, startDate: null, endsAt: null, cancelAtPeriodEnd: false, tier: 'free'
                };
            }
            if (!userData.preferences || !userData.preferences.adWatchData) {
                updates['preferences.adWatchData'] = {
                  lastAdWatchedAt: null, adFreeUntil: null, adsWatchedToday: 0, lastAdWatchDay: null
                };
            }
            // Assurez-vous que l'audioConfig existe et a la bonne structure
            if (!userData.preferences || !userData.preferences.audioConfig) {
                 updates['preferences.audioConfig'] = { // Initialiser avec la nouvelle structure
                    enableMusic: false, music: { volume: 50, url: '' },
                    enableAmbient: false, ambient: { volume: 50, url: '' },
                    enableBinaural: false, binaural: { volume: 50, baseFreq: 100, beatFreq: 10 },
                    voice: { volume: 100, language: 'fr', provider: 'browser', voice: 'auto', gender: 'auto' },
                };
            } else {
                // Si audioConfig existe mais manque de sous-objets ou de champs
                if (!userData.preferences.audioConfig.music) updates['preferences.audioConfig.music'] = { volume: 50, url: '' };
                if (!userData.preferences.audioConfig.ambient) updates['preferences.audioConfig.ambient'] = { volume: 50, url: '' };
                if (!userData.preferences.audioConfig.binaural) updates['preferences.audioConfig.binaural'] = { volume: 50, baseFreq: 100, beatFreq: 10 };
                if (!userData.preferences.audioConfig.voice) updates['preferences.audioConfig.voice'] = { volume: 100, language: 'fr', provider: 'browser', voice: 'auto', gender: 'auto' };

                // Si d'anciennes propriétés aplaties existent, migrez-les ou supprimez-les si le schéma est définitif
                // Exemple: if (typeof userData.preferences.audioConfig.musicVolume !== 'undefined') {
                //   updates['preferences.audioConfig.music.volume'] = userData.preferences.audioConfig.musicVolume;
                //   updates['preferences.audioConfig.musicVolume'] = deleteField(); // Pour supprimer l'ancienne propriété
                // }
            }

            if (Object.keys(updates).length > 0) {
              await updateDoc(userDocRef, updates);
            }
        }
      }
    });

    return () => unsubscribe();
  }, [t]);

  return { user, loading };
};