// src/store/slices/userProfileSlice.ts
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { doc, getDoc, updateDoc, Timestamp } from 'firebase/firestore';
import { db } from '../../firebase';

/**
 * Fonction utilitaire pour convertir récursivement les Timestamps de Firestore en chaînes ISO.
 * Gère les objets imbriqués et les collections Firestore.
 * @param data L'objet de données à traiter.
 * @returns Une copie de l'objet avec les Timestamps convertis.
 */
function convertTimestampsToISOStrings<T extends Record<string, any>>(data: T): T {
  const newData: T = { ...data };
  for (const key in newData) {
    if (newData.hasOwnProperty(key)) {
      const value = newData[key];

      // Vérification pour l'objet Timestamp de Firestore
      if (value && typeof value === 'object' && typeof (value as Timestamp).toDate === 'function') {
        newData[key] = (value as Timestamp).toDate().toISOString() as any;
      }
      // Si la valeur est un objet (non null, non tableau), récursivement convertir
      else if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        newData[key] = convertTimestampsToISOStrings(value);
      }
    }
  }
  return newData;
}

// --- MISE À JOUR CRUCIALE DE L'INTERFACE AudioConfig ---
export interface AudioConfig {
  enableMusic?: boolean;
  music?: { url: string; volume?: number; }; // url est maintenant obligatoire (string, pas string | undefined)
  enableAmbient?: boolean;
  ambient?: { url: string; volume?: number; }; // url est maintenant obligatoire (string, pas string | undefined)
  enableBinaural?: boolean;
  binaural?: { baseFreq?: number; beatFreq?: number; volume?: number; };
  voice?: { volume?: number; language?: string; provider?: string; voice?: string; gender?: 'masculine' | 'feminine' | 'neutral'; }; // <-- Ajout de 'neutral' pour gender
}

export interface SubscriptionStatus {
  active: boolean;
  startDate: string | null;
  endsAt: string | null;
  cancelAtPeriodEnd: boolean;
  tier: 'free' | 'premium';
}

export interface AdWatchData {
  lastAdWatchedAt: string | null;
  adFreeUntil: string | null;
  adsWatchedToday: number;
  lastAdWatchDay: string | null;
}

// ... (Rest of existing types like GrammaticalGender, SessionStats, SessionNote, GameStats, UserStats) ...
export type GrammaticalGender = 'masculine' | 'feminine' | 'neutral';

export interface SessionStats {
  sessionId: number | string;
  count: number;
  totalDuration: number;
}

export interface SessionNote {
  date: string;
  note: string;
  mood: string;
}

export interface GameStats {
  gameId: string | number;
  totalPlayed: number;
  totalScore: number;
  bestScore: number;
}

export interface UserStats {
  totalSessionsPlayed: number;
  totalMinutesPlayed: number;
  sessionsPlayed?: { [sessionId: string]: SessionStats };
  lastSessionPlayedAt: string | null;
  currentStreak: number;
  longestStreak: number;
  totalNotesWritten: number;
  totalSessionsWithNotes: number;
  sessionsNotes?: { [sessionId: string]: { [noteId: string]: SessionNote } };
  favoriteSessionId: string[];
  gameStats?: { [gameId: string]: GameStats };
}


export interface UserPreferences {
  theme: 'light' | 'dark';
  language: string;
  notificationsEnabled: boolean;
  preferedGender: GrammaticalGender;
  audioConfig: AudioConfig;
  unlockedFeatures: string[];
  subscriptions: SubscriptionStatus;
  adWatchData: AdWatchData;
  stats: UserStats;
}

export interface UserProfileData {
  uid: string;
  email: string | null;
  createdAt: string;
  lastLoginAt: string;
  publicName: string;
  preferences: UserPreferences;
}

interface UserProfileState {
  profile: UserProfileData | null;
  loading: boolean;
  saving: boolean;
  error: string | null;
}

const initialState: UserProfileState = {
  profile: null,
  loading: false,
  saving: false,
  error: null,
};

export const fetchUserProfile = createAsyncThunk(
  'userProfile/fetchUserProfile',
  async (userId: string, { rejectWithValue }) => {
    try {
      const userDocRef = doc(db, 'users', userId);
      const docSnap = await getDoc(userDocRef);

      if (docSnap.exists()) {
        const data = docSnap.data();
        const processedData = convertTimestampsToISOStrings(data);
        return { uid: userId, ...processedData } as UserProfileData;
      } else {
        return rejectWithValue('Profil utilisateur non trouvé.');
      }
    } catch (error: any) {
      console.error("Error fetching user profile:", error);
      return rejectWithValue(error.message);
    }
  }
);

// Pour updateUserProfile, assurez-vous que les données envoyées correspondent à la nouvelle structure
export const updateUserProfile = createAsyncThunk(
  'userProfile/updateUserProfile',
  async ({ userId, data }: { userId: string, data: Partial<UserProfileData> }, { rejectWithValue }) => {
    try {
      const userDocRef = doc(db, 'users', userId);
      await updateDoc(userDocRef, data);
      return data;
    } catch (error: any) {
      console.error("Error updating user profile:", error);
      return rejectWithValue(error.message);
    }
  }
);

export const updateAdWatchData = createAsyncThunk(
  'userProfile/updateAdWatchData',
  async ({ userId, data }: { userId: string, data: Partial<AdWatchData> }, { getState, rejectWithValue }) => {
    try {
      const userDocRef = doc(db, 'users', userId);
      await updateDoc(userDocRef, { 'preferences.adWatchData': data });
      return data;
    } catch (error: any) {
      console.error("Error updating ad watch data:", error);
      return rejectWithValue(error.message);
    }
  }
);


const userProfileSlice = createSlice({
  name: 'userProfile',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchUserProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserProfile.fulfilled, (state, action: PayloadAction<UserProfileData>) => {
        state.loading = false;
        state.profile = action.payload;
      })
      .addCase(fetchUserProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(updateUserProfile.pending, (state) => {
        state.saving = true;
        state.error = null;
      })
      .addCase(updateUserProfile.fulfilled, (state, action: PayloadAction<Partial<UserProfileData>>) => {
        state.saving = false;
        if (state.profile) {
          state.profile = { ...state.profile, ...action.payload };
          if (action.payload.preferences) {
            state.profile.preferences = { ...state.profile.preferences, ...action.payload.preferences };
            if (action.payload.preferences.stats) {
              state.profile.preferences.stats = { ...state.profile.preferences.stats, ...action.payload.preferences.stats };
            }
            // MISE À JOUR IMPORTANTE pour AudioConfig et Subscriptions
            if (action.payload.preferences.audioConfig) {
              state.profile.preferences.audioConfig = { ...state.profile.preferences.audioConfig, ...action.payload.preferences.audioConfig };
            }
            if (action.payload.preferences.subscriptions) {
              state.profile.preferences.subscriptions = { ...state.profile.preferences.subscriptions, ...action.payload.preferences.subscriptions };
            }
            if (action.payload.preferences.adWatchData) {
              state.profile.preferences.adWatchData = { ...state.profile.preferences.adWatchData, ...action.payload.preferences.adWatchData };
            }
          }
        }
      })
      .addCase(updateUserProfile.rejected, (state, action) => {
        state.saving = false;
        state.error = action.payload as string;
      })
      .addCase(updateAdWatchData.fulfilled, (state, action: PayloadAction<Partial<AdWatchData>>) => {
        if (state.profile && state.profile.preferences) {
          state.profile.preferences.adWatchData = {
            ...state.profile.preferences.adWatchData,
            ...action.payload
          };
        }
      })
      .addCase(updateAdWatchData.rejected, (state, action) => {
        console.error("Failed to update ad watch data:", action.payload);
      });
  },
});

export default userProfileSlice.reducer;