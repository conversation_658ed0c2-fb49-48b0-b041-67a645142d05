import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { persistStore, persistReducer, FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER } from 'redux-persist';
import storage from 'redux-persist/lib/storage';

// Import des slices EXISTANTES
import authSlice from './slices/authSlice';
import sessionsSlice from './slices/sessionsSlice';
import journalSlice from './slices/journalSlice';
import audioAssetsSlice from './slices/audioAssetsSlice';
import syncSlice from './slices/syncSlice';
import networkSlice from './slices/networkSlice';
// --- NOUVELLES IMPORTATIONS ---
import historySlice from './slices/historySlice';      // Importez historySlice
import userProfileSlice from './slices/userProfileSlice'; // Importez userProfileSlice

// Configuration de la persistance
const persistConfig = {
  key: 'piknowkyo-root',
  version: 1,
  storage,
  // --- MISE À JOUR DE LA WHITELIST ---
  // Assurez-vous que 'userProfile' et 'history' sont inclus si vous voulez les persister
  whitelist: ['auth', 'sessions', 'journal', 'audioAssets', 'sync', 'userProfile', 'history'],
};

// Configuration spécifique pour les données sensibles (ex: auth)
const authPersistConfig = {
  key: 'auth',
  storage,
};

// Combinaison des reducers
const rootReducer = combineReducers({
  auth: persistReducer(authPersistConfig, authSlice), // auth slice est persistée séparément
  sessions: sessionsSlice,
  journal: journalSlice,
  audioAssets: audioAssetsSlice,
  sync: syncSlice,
  network: networkSlice,
  // --- AJOUTEZ LES NOUVEAUX REDUCERS ICI ---
  history: historySlice,      // Ajoutez historySlice
  userProfile: userProfileSlice, // Ajoutez userProfileSlice
});

const persistedReducer = persistReducer(persistConfig, rootReducer);

// Configuration du store
const store = configureStore({
  reducer: persistedReducer, // Utilisez le reducer persisté
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
        // --- MISE À JOUR POUR LES CHEMINS NON SÉRIALISABLES DE USERPROFILE ---
        // Ajoutez les chemins spécifiques pour userProfile si vous stockez des non-sériables comme des Date objects
        ignoredPaths: [
          'userProfile.profile.createdAt',
          'userProfile.profile.lastLoginAt',
          'userProfile.profile.preferences.subscriptions.startDate',
          'userProfile.profile.preferences.subscriptions.endsAt',
          'userProfile.profile.preferences.stats.lastSessionPlayedAt',
          // Ajoutez d'autres chemins de userProfile ou de history si nécessaire
        ],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
});

const persistor = persistStore(store);

// Types pour TypeScript (gardez ces exports pour useAppSelector/useAppDispatch)
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export { store, persistor }; // Exportez store et persistor