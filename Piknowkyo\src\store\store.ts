// src/store/store.ts
import { configureStore } from '@reduxjs/toolkit';
import sessionsReducer from './slices/sessionsSlice';
import journalReducer from './slices/journalSlice';
import audioAssetsReducer from './slices/audioAssetsSlice';
import historyReducer from './slices/historySlice';
import userProfileReducer from './slices/userProfileSlice'; // Importez la nouvelle slice

export const store = configureStore({
  reducer: {
    sessions: sessionsReducer,
    journal: journalReducer,
    audioAssets: audioAssetsReducer,
    history: historyReducer,
    userProfile: userProfileReducer, // Ajoutez-le ici
    // ... autres reducers
  },
  middleware: (getDefaultMiddleware) => getDefaultMiddleware({
    serializableCheck: {
      // Ignorer les Date objects pour Firestore, car ils ne sont pas toujours sérialisables par défaut
      // Si vous convertissez tout en ISO string pour Firestore, cela peut ne pas être nécessaire.
      // Si vous stockez des objets Date bruts, vous pourriez avoir besoin de ça.
      ignoredPaths: [
        'userProfile.profile.createdAt',
        'userProfile.profile.lastLoginAt',
        'userProfile.profile.preferences.subscriptions.startDate',
        'userProfile.profile.preferences.subscriptions.endsAt',
        'userProfile.profile.preferences.stats.lastSessionPlayedAt',
        // Ajoutez d'autres chemins si vous stockez des non-sériables
      ],
    },
  }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;