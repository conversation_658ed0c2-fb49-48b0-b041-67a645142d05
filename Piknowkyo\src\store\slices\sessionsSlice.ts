// src/store/slices/sessionsSlice.ts
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { collection, getDocs } from 'firebase/firestore'; // <-- Importez les fonctions Firestore
import { db } from '../../firebase'; // <-- Importez votre instance Firestore

// Type de catégorie (pour la cohérence)
export type SessionCategory = 'meditation' | 'story' | 'exercise' | 'challenge' | 'other';

export interface Session {
  id: string;
  title: string;
  description: string;
  audioUrl: string;
  duration: number; // en secondes
  category: SessionCategory; // <-- NOUVEAU: La catégorie de la session
  imageUrl?: string; // Ajouté si vos sessions peuvent avoir des images
  benefits?: string[]; // Ajouté si vos sessions ont des bénéfices
  tags?: string[]; // Ajouté si vos sessions ont des tags
  rating?: number; // Ajouté si vos sessions ont une évaluation
  comments?: string[]; // Ajouté si vos sessions ont des commentaires
  script?: { text: string; pause: number; rate?: number; pitch?: number; }[]; // Ajouté si les scripts sont ici
  audio?: { // Configuration audio par défaut pour la session (si vous l'avez dans votre DB)
    enableMusic?: boolean;
    music?: { url: string; volume: number; };
    enableAmbient?: boolean;
    ambient?: { url: string; volume: number; };
    enableBinaural?: boolean;
    binaural?: { baseFreq: number; beatFreq: number; volume: number; };
    voice?: { volume: number; };
  };
  // Ajoutez d'autres propriétés de votre modèle Session ici
}

export interface SessionsState {
  sessions: Session[];
  currentSession: Session | null;
  loading: boolean; // <-- CHANGEMENT: isLoading devient loading
  error: string | null;
  lastSyncTimestamp: number | null;
  pendingChanges: {
    created: Session[];
    updated: Session[];
    deleted: string[];
  };
}

const initialState: SessionsState = {
  sessions: [],
  currentSession: null,
  loading: false, // <-- CHANGEMENT
  error: null,
  lastSyncTimestamp: null,
  pendingChanges: {
    created: [],
    updated: [],
    deleted: [],
  },
};

// Actions asynchrones
export const fetchSessions = createAsyncThunk(
  'sessions/fetchSessions',
  async (_, { rejectWithValue }) => {
    try {
      // <-- REMPLACEZ LA LECTURE DU JSON LOCAL PAR UN APPEL FIRESTORE -->
      const sessionsCollection = collection(db, 'sessions');
      const querySnapshot = await getDocs(sessionsCollection);
      const sessions = querySnapshot.docs.map(doc => ({
        id: doc.id,
        // Assurez-vous que toutes les propriétés de Session (y compris la catégorie) sont présentes dans doc.data()
        // et que les Timestamps sont bien convertis en chaînes ISO si nécessaire.
        // Si vous utilisez `convertTimestampsToISOStrings` comme dans userProfileSlice, vous pourriez l'appliquer ici aussi.
        ...doc.data()
      })) as Session[];
      return sessions;
    } catch (error: any) {
      console.error("Error fetching sessions from Firestore:", error);
      return rejectWithValue(error.message);
    }
  }
);

export const createSession = createAsyncThunk(
  'sessions/createSession',
  async (sessionData: Omit<Session, 'id'>, { rejectWithValue }) => {
    // Cette thunk est actuellement "offline-first" ou pour une logique locale.
    // Si vous voulez que la création de session soit persistée dans Firestore,
    // vous devrez ajouter ici un appel `addDoc(collection(db, 'sessions'), sessionData);`
    // et gérer le retour de l'ID généré par Firestore.
    try {
      const newSession: Session = {
        ...sessionData,
        id: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      };

      return newSession;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

const sessionsSlice = createSlice({
  name: 'sessions',
  initialState,
  reducers: {
    setCurrentSession: (state, action: PayloadAction<Session | null>) => {
      state.currentSession = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    updateLastSyncTimestamp: (state, action: PayloadAction<number>) => {
      state.lastSyncTimestamp = action.payload;
    },
    clearPendingChanges: (state) => {
      state.pendingChanges = {
        created: [],
        updated: [],
        deleted: [],
      };
    },
    // Ajoutez ici des reducers pour gérer les sessions créées/modifiées/supprimées si vous utilisez pendingChanges
    // et que vous avez des actions correspondantes pour les pousser vers Firestore.
  },
  extraReducers: (builder) => {
    builder
      // Fetch Sessions
      .addCase(fetchSessions.pending, (state) => {
        state.loading = true; // <-- CHANGEMENT
        state.error = null;
      })
      .addCase(fetchSessions.fulfilled, (state, action) => {
        state.loading = false; // <-- CHANGEMENT
        state.sessions = action.payload;
        state.lastSyncTimestamp = Date.now();
      })
      .addCase(fetchSessions.rejected, (state, action) => {
        state.loading = false; // <-- CHANGEMENT
        state.error = action.payload as string;
      })
      // Create Session
      .addCase(createSession.pending, (state) => {
        state.loading = true; // <-- CHANGEMENT
        state.error = null;
      })
      .addCase(createSession.fulfilled, (state, action) => {
        state.loading = false; // <-- CHANGEMENT
        state.sessions.push(action.payload);
        
        // Si offline, ajouter aux changements en attente
        if (!navigator.onLine) { // Cette logique est basée sur la connectivité client
          state.pendingChanges.created.push(action.payload);
        }
      })
      .addCase(createSession.rejected, (state, action) => {
        state.loading = false; // <-- CHANGEMENT
        state.error = action.payload as string;
      });
  },
});

export const {
  setCurrentSession,
  clearError,
  updateLastSyncTimestamp,
  clearPendingChanges,
} = sessionsSlice.actions;

export default sessionsSlice.reducer;